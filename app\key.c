#include "key.h"
volatile int Flag_stop;
volatile int Flag_stop1;
/**************************************************************************
Function: Key scan
Input   : Double click the waiting time
Output  : 0：No action；1：click；2：Double click
函数功能：按键扫描
入口参数：双击等待时间
返回  值：按键状态 0：无动作 1：单击 2：双击
**************************************************************************/
// uint8_t click_N_Double (uint8_t time)
// {
//     static  uint8_t flag_key,count_key,double_key=0;
//     static  uint16_t count_single,Forever_count;
//     if(DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)>0)  Forever_count++;   //长按标志位未置1
//     else        Forever_count=0;
//     if((DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)>0)&&0==flag_key)     flag_key=1; //第一次按下
//     if(0==count_key)
//     {
//             if(flag_key==1)
//             {
//                 double_key++;
//                 count_key=1;            //标记按下一次
//             }
//             if(double_key==3)
//             {                                       //按下两次
//                 double_key=0;
//                 count_single=0;
//                 return 2;                   //双击执行的指令
//             }
//     }
//     if(0==DL_GPIO_readPins(KEY_PORT,KEY_key_PIN))          flag_key=0,count_key=0;
//     if(1==double_key)
//     {
//         count_single++;
//         if(count_single>time&&Forever_count<time)
//         {
//             double_key=0;
//             count_single=0; //超时不标记为双击
// 			return 1;//单击执行的指令
//         }
//         if(Forever_count>time)
//         {
//             double_key=0;
//             count_single=0;
//         }
//     }
//     return 0;
// }
// /**************************************************************************
// Function: Long press detection
// Input   : none
// Output  : 0：No action；1：Long press for 2 seconds；
// 函数功能：长按检测
// 入口参数：无
// 返回  值：按键状态 0：无动作 1：长按2s
// **************************************************************************/
// uint8_t Long_Press(void)
// {
//         static uint16_t Long_Press_count,Long_Press;
//       if(Long_Press==0&&KEY==0)  Long_Press_count++;   //长按标志位未置1
//     else                       Long_Press_count=0;
//         if(Long_Press_count>200)        //长按标志位置1
//       {
//             Long_Press=1;
//             Long_Press_count=0;
//             return 1;
//         }
//         if(Long_Press==1)     //单击控制小车的启停
//         {
//             Long_Press=0;
//         }
//         return 0;
// }


// void Key(void)
// {
// 	u8 tmp,tmp2;
// 	tmp=click_N_Double(50);
// 	if(tmp==1)
// 	{
// 		Flag_Stop=!Flag_Stop;
//     }
// }
// void Key(void)
// {
// 	uint8_t tmp,tmp2;
// 	tmp=click_N_Double(50);
// 	if(tmp==1)
// 	{
// 		Flag_stop = 1;
//     }
//     else 
//     {
//         Flag_stop = 0;
//     }
// }

void Key(void)
{
    if(DL_GPIO_readPins(KEY_key_PORT, KEY_key_PIN))
    {
        while (DL_GPIO_readPins(KEY_key_PORT, KEY_key_PIN));
        Flag_stop = 1;
    }
    else 
    {
        Flag_stop = 0;
    
    }

}
void Key_1(void)
{
    if(DL_GPIO_readPins(KEY_PIN_3_PORT, KEY_PIN_3_PIN))
    {
        while (DL_GPIO_readPins(KEY_PIN_3_PORT, KEY_PIN_3_PIN));
        Flag_stop1 = 1;
    }
    else 
    {
        Flag_stop1 = 0;
    }

}
