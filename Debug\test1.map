******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 12:29:14 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002a25


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004c48  0001b3b8  R  X
  SRAM                  20200000   00008000  00000923  000076dd  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c48   00004c48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000030e0   000030e0    r-x .text
  000031a0    000031a0    00001a50   00001a50    r-- .rodata
  00004bf0    00004bf0    00000058   00000058    r-- .cinit
20200000    20200000    0000072a   00000000    rw-
  20200000    20200000    00000561   00000000    rw- .bss
  20200568    20200568    000001c2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000030e0     
                  000000c0    00000580     Ganway.o (.text.Way)
                  00000640    000001d0     oled.o (.text.OLED_ShowChar)
                  00000810    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009a4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b36    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b38    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000cc0    00000128     empty.o (.text.TIMG0_IRQHandler)
                  00000de8    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f08    0000010c     motor.o (.text.Set_PWM)
                  00001014    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001120    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001224    000000ec     empty.o (.text.main)
                  00001310    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000013f8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000014dc    000000e2     oled.o (.text.OLED_ShowNum)
                  000015be    000000de     oled.o (.text.OLED_Init)
                  0000169c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001778    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001848    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000018f2    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  0000198c    0000009a     oled.o (.text.OLED_ShowString)
                  00001a26    00000002     --HOLE-- [fill = 0]
                  00001a28    00000090     oled.o (.text.OLED_DrawPoint)
                  00001ab8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b44    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001bd0    00000084     oled.o (.text.OLED_Refresh)
                  00001c54    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001cd8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001d54    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001dc8    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001e3a    00000002     --HOLE-- [fill = 0]
                  00001e3c    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001ea8    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001f14    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001f7c    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001fde    00000002     --HOLE-- [fill = 0]
                  00001fe0    00000060     oled.o (.text.OLED_Clear)
                  00002040    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000209e    00000002     --HOLE-- [fill = 0]
                  000020a0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000020f8    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000214c    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  0000219c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000021ec    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002238    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002284    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000022ce    00000002     --HOLE-- [fill = 0]
                  000022d0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000231a    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002364    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000023ac    00000048     oled.o (.text.OLED_DisplayTurn)
                  000023f4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  0000243c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002484    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000024c8    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000250a    00000002     --HOLE-- [fill = 0]
                  0000250c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  0000254c    00000040     key.o (.text.Key)
                  0000258c    00000040     key.o (.text.Key_1)
                  000025cc    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  0000260c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000264c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002688    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000026c4    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002700    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000273c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00002776    00000002     --HOLE-- [fill = 0]
                  00002778    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000027ac    00000034     oled.o (.text.OLED_ColorTurn)
                  000027e0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002814    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002848    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00002878    00000030     oled.o (.text.OLED_Pow)
                  000028a8    00000030     systick.o (.text.SysTick_Handler)
                  000028d8    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00002904    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002930    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000295c    00000028     empty.o (.text.DL_Common_updateReg)
                  00002984    00000028     oled.o (.text.DL_Common_updateReg)
                  000029ac    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000029d4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000029fc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002a24    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002a4c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002a72    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002a98    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002abc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002adc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002afc    00000020     systick.o (.text.delay_ms)
                  00002b1c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002b3a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002b58    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002b74    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002b90    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bc8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002be4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002c00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002c1c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002c38    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002c54    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002c70    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002c8c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002ca8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002cc4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002cdc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002cf4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002d0c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002d24    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002d3c    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002d54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002d6c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002d84    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002d9c    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002db4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002dcc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002de4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002dfc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002e14    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002e2c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002e44    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002e5c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002e74    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002e8c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002ea4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002ebc    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002ed4    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002eec    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002f04    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002f1a    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002f30    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002f46    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002f5c    00000016     key.o (.text.DL_GPIO_readPins)
                  00002f72    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002f88    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002f9c    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00002fb0    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00002fc4    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002fd8    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00002fec    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003000    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003014    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003028    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000303c    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003050    00000014     motor.o (.text.Left_Control)
                  00003064    00000014     motor.o (.text.Right_Control)
                  00003078    00000014     motor.o (.text.Right_Little_Control)
                  0000308c    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  0000309e    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000030b0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000030c2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000030d4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000030e6    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  000030f6    00000002     --HOLE-- [fill = 0]
                  000030f8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003108    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003118    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003128    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003136    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003144    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003150    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000315c    0000000c     systick.o (.text.get_systicks)
                  00003168    0000000c     Scheduler.o (.text.scheduler_init)
                  00003174    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000317e    00000002     --HOLE-- [fill = 0]
                  00003180    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003188    00000006     libc.a : exit.c.obj (.text:abort)
                  0000318e    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003192    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003196    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000319a    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000319e    00000002     --HOLE-- [fill = 0]

.cinit     0    00004bf0    00000058     
                  00004bf0    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  00004c1f    00000001     --HOLE-- [fill = 0]
                  00004c20    0000000c     (__TI_handler_table)
                  00004c2c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004c34    00000010     (__TI_cinit_table)
                  00004c44    00000004     --HOLE-- [fill = 0]

.rodata    0    000031a0    00001a50     
                  000031a0    00000d5c     oled.o (.rodata.asc2_2412)
                  00003efc    000005f0     oled.o (.rodata.asc2_1608)
                  000044ec    00000474     oled.o (.rodata.asc2_1206)
                  00004960    00000228     oled.o (.rodata.asc2_0806)
                  00004b88    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004bb0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004bc4    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004bce    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004bd0    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004bd8    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004be0    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004be3    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004be6    00000003     empty.o (.rodata.str1.254342170260855183.1)
                  00004be9    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004bec    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004bee    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000561     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:Run)
                  20200550    00000004     (.common:encoderA_cnt)
                  20200554    00000004     (.common:encoderB_cnt)
                  20200558    00000004     (.common:gpio_interrup1)
                  2020055c    00000004     (.common:gpio_interrup2)
                  20200560    00000001     (.common:task_num)

.data      0    20200568    000001c2     UNINITIALIZED
                  20200568    00000100     empty.o (.data.rx_buff)
                  20200668    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e8    00000010     empty.o (.data.Anolog)
                  202006f8    00000010     empty.o (.data.black)
                  20200708    00000010     empty.o (.data.white)
                  20200718    00000008     systick.o (.data.systicks)
                  20200720    00000004     empty.o (.data.D_Num)
                  20200724    00000004     systick.o (.data.delay_times)
                  20200728    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200729    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          798     6         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3578    294       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway.o                         1408    0         0      
       encoder.o                        362     0         16     
       motor.o                          372     0         0      
       key.o                            150     0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3718    0         17     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12492   7009      2339   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c34 records: 2, size/record: 8, table size: 16
	.data: load addr=00004bf0, load size=0000002f bytes, run addr=20200568, run size=000001c2 bytes, compression=lzss
	.bss: load addr=00004c2c, load size=00000008 bytes, run addr=20200000, run size=00000561 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c20 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000318f  ADC0_IRQHandler                      
0000318f  ADC1_IRQHandler                      
0000318f  AES_IRQHandler                       
202006e8  Anolog                               
00003192  C$$EXIT                              
0000318f  CANFD0_IRQHandler                    
0000318f  DAC0_IRQHandler                      
0000250d  DL_ADC12_setClockConfig              
00003175  DL_Common_delayCycles                
00002041  DL_I2C_fillControllerTXFIFO          
00002a73  DL_I2C_setClockConfig                
0000169d  DL_SYSCTL_configSYSPLL               
00002485  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001121  DL_Timer_initFourCCPWMMode           
00001311  DL_Timer_initTimerMode               
00002c71  DL_Timer_setCaptCompUpdateMethod     
00002ea5  DL_Timer_setCaptureCompareOutCtl     
00003109  DL_Timer_setCaptureCompareValue      
00002c8d  DL_Timer_setClockConfig              
00002365  DL_UART_init                         
000030b1  DL_UART_setClockConfig               
0000318f  DMA_IRQHandler                       
20200720  D_Num                                
0000318f  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
0000318f  GROUP0_IRQHandler                    
00000de9  GROUP1_IRQHandler                    
00001779  Get_Analog_value                     
000026c5  Get_Anolog_Value                     
00003129  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003193  HOSTexit                             
0000318f  HardFault_Handler                    
0000318f  I2C0_IRQHandler                      
0000318f  I2C1_IRQHandler                      
0000254d  Key                                  
0000258d  Key_1                                
00003051  Left_Control                         
0000318f  NMI_Handler                          
00000b39  No_MCU_Ganv_Sensor_Init              
00001dc9  No_MCU_Ganv_Sensor_Init_Frist        
000024c9  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001fe1  OLED_Clear                           
000027ad  OLED_ColorTurn                       
000023ad  OLED_DisplayTurn                     
00001a29  OLED_DrawPoint                       
20200000  OLED_GRAM                            
000015bf  OLED_Init                            
00002879  OLED_Pow                             
00001bd1  OLED_Refresh                         
00000641  OLED_ShowChar                        
000014dd  OLED_ShowNum                         
000018f3  OLED_ShowSignedNum                   
0000198d  OLED_ShowString                      
00001e3d  OLED_WR_Byte                         
0000318f  PendSV_Handler                       
0000318f  RTC_IRQHandler                       
00003197  Reset_Handler                        
00003065  Right_Control                        
00003079  Right_Little_Control                 
2020054c  Run                                  
0000318f  SPI0_IRQHandler                      
0000318f  SPI1_IRQHandler                      
0000318f  SVC_Handler                          
000023f5  SYSCFG_DL_ADC12_0_init               
00000811  SYSCFG_DL_GPIO_init                  
000020a1  SYSCFG_DL_I2C_OLED_init              
00001ab9  SYSCFG_DL_PWM_0_init                 
0000243d  SYSCFG_DL_SYSCTL_init                
00003145  SYSCFG_DL_SYSTICK_init               
000027e1  SYSCFG_DL_TIMER_0_init               
000020f9  SYSCFG_DL_UART_0_init                
00002815  SYSCFG_DL_init                       
00001b45  SYSCFG_DL_initPower                  
00000f09  Set_PWM                              
000028a9  SysTick_Handler                      
0000318f  TIMA0_IRQHandler                     
0000318f  TIMA1_IRQHandler                     
00000cc1  TIMG0_IRQHandler                     
0000318f  TIMG12_IRQHandler                    
0000318f  TIMG6_IRQHandler                     
0000318f  TIMG7_IRQHandler                     
0000318f  TIMG8_IRQHandler                     
000030c3  TI_memcpy_small                      
00003137  TI_memset_small                      
000025cd  UART0_IRQHandler                     
0000318f  UART1_IRQHandler                     
0000318f  UART2_IRQHandler                     
0000318f  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004c34  __TI_CINIT_Base                      
00004c44  __TI_CINIT_Limit                     
00004c44  __TI_CINIT_Warm                      
00004c20  __TI_Handler_Table_Base              
00004c2c  __TI_Handler_Table_Limit             
00002701  __TI_auto_init_nobinit_nopinit       
00001cd9  __TI_decompress_lzss                 
000030d5  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003119  __TI_zero_init                       
000009af  __adddf3                             
000022d1  __aeabi_d2iz                         
000009af  __aeabi_dadd                         
00001f7d  __aeabi_dcmpeq                       
00001fb9  __aeabi_dcmpge                       
00001fcd  __aeabi_dcmpgt                       
00001fa5  __aeabi_dcmple                       
00001f91  __aeabi_dcmplt                       
00001015  __aeabi_ddiv                         
000013f9  __aeabi_dmul                         
000009a5  __aeabi_dsub                         
00002931  __aeabi_i2d                          
00000b37  __aeabi_idiv0                        
00003151  __aeabi_memclr                       
00003151  __aeabi_memclr4                      
00003151  __aeabi_memclr8                      
00003181  __aeabi_memcpy                       
00003181  __aeabi_memcpy4                      
00003181  __aeabi_memcpy8                      
00002a99  __aeabi_ui2d                         
0000260d  __aeabi_uidiv                        
0000260d  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001f15  __cmpdf2                             
00001015  __divdf3                             
00001f15  __eqdf2                              
000022d1  __fixdfsi                            
00002931  __floatsidf                          
00002a99  __floatunsidf                        
00001d55  __gedf2                              
00001d55  __gtdf2                              
00001f15  __ledf2                              
00001f15  __ltdf2                              
UNDEFED   __mpu_init                           
000013f9  __muldf3                             
0000273d  __muldsi3                            
00001f15  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009a5  __subdf3                             
00002a25  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000319b  _system_pre_init                     
00003189  abort                                
0000231b  adc_getValue                         
00004960  asc2_0806                            
000044ec  asc2_1206                            
00003efc  asc2_1608                            
000031a0  asc2_2412                            
ffffffff  binit                                
202006f8  black                                
00001ea9  convertAnalogToDigital               
00002afd  delay_ms                             
20200724  delay_times                          
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200480  gPWM_0Backup                         
0000315d  get_systicks                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
00000000  interruptVectors                     
00001225  main                                 
00001849  normalizeAnalogValues                
20200568  rx_buff                              
00003169  scheduler_init                       
20200560  task_num                             
20200668  uart_rx_buffer                       
20200728  uart_rx_index                        
20200729  uart_rx_ticks                        
20200708  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000641  OLED_ShowChar                        
00000811  SYSCFG_DL_GPIO_init                  
000009a5  __aeabi_dsub                         
000009a5  __subdf3                             
000009af  __adddf3                             
000009af  __aeabi_dadd                         
00000b37  __aeabi_idiv0                        
00000b39  No_MCU_Ganv_Sensor_Init              
00000cc1  TIMG0_IRQHandler                     
00000de9  GROUP1_IRQHandler                    
00000f09  Set_PWM                              
00001015  __aeabi_ddiv                         
00001015  __divdf3                             
00001121  DL_Timer_initFourCCPWMMode           
00001225  main                                 
00001311  DL_Timer_initTimerMode               
000013f9  __aeabi_dmul                         
000013f9  __muldf3                             
000014dd  OLED_ShowNum                         
000015bf  OLED_Init                            
0000169d  DL_SYSCTL_configSYSPLL               
00001779  Get_Analog_value                     
00001849  normalizeAnalogValues                
000018f3  OLED_ShowSignedNum                   
0000198d  OLED_ShowString                      
00001a29  OLED_DrawPoint                       
00001ab9  SYSCFG_DL_PWM_0_init                 
00001b45  SYSCFG_DL_initPower                  
00001bd1  OLED_Refresh                         
00001cd9  __TI_decompress_lzss                 
00001d55  __gedf2                              
00001d55  __gtdf2                              
00001dc9  No_MCU_Ganv_Sensor_Init_Frist        
00001e3d  OLED_WR_Byte                         
00001ea9  convertAnalogToDigital               
00001f15  __cmpdf2                             
00001f15  __eqdf2                              
00001f15  __ledf2                              
00001f15  __ltdf2                              
00001f15  __nedf2                              
00001f7d  __aeabi_dcmpeq                       
00001f91  __aeabi_dcmplt                       
00001fa5  __aeabi_dcmple                       
00001fb9  __aeabi_dcmpge                       
00001fcd  __aeabi_dcmpgt                       
00001fe1  OLED_Clear                           
00002041  DL_I2C_fillControllerTXFIFO          
000020a1  SYSCFG_DL_I2C_OLED_init              
000020f9  SYSCFG_DL_UART_0_init                
000022d1  __aeabi_d2iz                         
000022d1  __fixdfsi                            
0000231b  adc_getValue                         
00002365  DL_UART_init                         
000023ad  OLED_DisplayTurn                     
000023f5  SYSCFG_DL_ADC12_0_init               
0000243d  SYSCFG_DL_SYSCTL_init                
00002485  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000024c9  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000250d  DL_ADC12_setClockConfig              
0000254d  Key                                  
0000258d  Key_1                                
000025cd  UART0_IRQHandler                     
0000260d  __aeabi_uidiv                        
0000260d  __aeabi_uidivmod                     
000026c5  Get_Anolog_Value                     
00002701  __TI_auto_init_nobinit_nopinit       
0000273d  __muldsi3                            
000027ad  OLED_ColorTurn                       
000027e1  SYSCFG_DL_TIMER_0_init               
00002815  SYSCFG_DL_init                       
00002879  OLED_Pow                             
000028a9  SysTick_Handler                      
00002931  __aeabi_i2d                          
00002931  __floatsidf                          
00002a25  _c_int00_noargs                      
00002a73  DL_I2C_setClockConfig                
00002a99  __aeabi_ui2d                         
00002a99  __floatunsidf                        
00002afd  delay_ms                             
00002c71  DL_Timer_setCaptCompUpdateMethod     
00002c8d  DL_Timer_setClockConfig              
00002ea5  DL_Timer_setCaptureCompareOutCtl     
00003051  Left_Control                         
00003065  Right_Control                        
00003079  Right_Little_Control                 
000030b1  DL_UART_setClockConfig               
000030c3  TI_memcpy_small                      
000030d5  __TI_decompress_none                 
00003109  DL_Timer_setCaptureCompareValue      
00003119  __TI_zero_init                       
00003129  Get_Digtal_For_User                  
00003137  TI_memset_small                      
00003145  SYSCFG_DL_SYSTICK_init               
00003151  __aeabi_memclr                       
00003151  __aeabi_memclr4                      
00003151  __aeabi_memclr8                      
0000315d  get_systicks                         
00003169  scheduler_init                       
00003175  DL_Common_delayCycles                
00003181  __aeabi_memcpy                       
00003181  __aeabi_memcpy4                      
00003181  __aeabi_memcpy8                      
00003189  abort                                
0000318f  ADC0_IRQHandler                      
0000318f  ADC1_IRQHandler                      
0000318f  AES_IRQHandler                       
0000318f  CANFD0_IRQHandler                    
0000318f  DAC0_IRQHandler                      
0000318f  DMA_IRQHandler                       
0000318f  Default_Handler                      
0000318f  GROUP0_IRQHandler                    
0000318f  HardFault_Handler                    
0000318f  I2C0_IRQHandler                      
0000318f  I2C1_IRQHandler                      
0000318f  NMI_Handler                          
0000318f  PendSV_Handler                       
0000318f  RTC_IRQHandler                       
0000318f  SPI0_IRQHandler                      
0000318f  SPI1_IRQHandler                      
0000318f  SVC_Handler                          
0000318f  TIMA0_IRQHandler                     
0000318f  TIMA1_IRQHandler                     
0000318f  TIMG12_IRQHandler                    
0000318f  TIMG6_IRQHandler                     
0000318f  TIMG7_IRQHandler                     
0000318f  TIMG8_IRQHandler                     
0000318f  UART1_IRQHandler                     
0000318f  UART2_IRQHandler                     
0000318f  UART3_IRQHandler                     
00003192  C$$EXIT                              
00003193  HOSTexit                             
00003197  Reset_Handler                        
0000319b  _system_pre_init                     
000031a0  asc2_2412                            
00003efc  asc2_1608                            
000044ec  asc2_1206                            
00004960  asc2_0806                            
00004c20  __TI_Handler_Table_Base              
00004c2c  __TI_Handler_Table_Limit             
00004c34  __TI_CINIT_Base                      
00004c44  __TI_CINIT_Limit                     
00004c44  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  Run                                  
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
20200560  task_num                             
20200568  rx_buff                              
20200668  uart_rx_buffer                       
202006e8  Anolog                               
202006f8  black                                
20200708  white                                
20200720  D_Num                                
20200724  delay_times                          
20200728  uart_rx_index                        
20200729  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[189 symbols]
